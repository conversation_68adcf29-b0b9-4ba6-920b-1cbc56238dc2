<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ChatFeedbackConversationRecordMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.ChatFeedbackConversationRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="feedback_id" jdbcType="VARCHAR" property="feedbackId" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="reply_feedback_status" jdbcType="VARCHAR" property="replyFeedbackStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
  </resultMap>

  <insert id="insert" parameterType="com.stock.service.platform.common.entity.ChatFeedbackConversationRecord">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into chat_feedback_conversation_record (id, feedback_id, content, 
      real_name, reply_feedback_status, create_time, 
      create_user)
    values (#{id,jdbcType=VARCHAR}, #{feedbackId,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}, 
      #{realName,jdbcType=VARCHAR}, #{replyFeedbackStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.ChatFeedbackConversationRecord">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into chat_feedback_conversation_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="feedbackId != null">
        feedback_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="replyFeedbackStatus != null">
        reply_feedback_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="feedbackId != null">
        #{feedbackId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="replyFeedbackStatus != null">
        #{replyFeedbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
